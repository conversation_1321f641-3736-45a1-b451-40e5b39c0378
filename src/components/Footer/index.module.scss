@use '../../../styles/media' as *;

.footer {
  width: 100%;
  background: var(--neutral-800);
  border-top: 1px solid var(--neutral-600);
  color: var(--neutral-200);
  padding: 32px 90px;

  @include laptop {
    padding: 30px;
  }

  @include tablet {
    padding: 30px 30px 94px;
  }

  @include mobile {
    padding: 35px 30px 94px;
  }
}

/* ——— top bar ——— */
.topBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;

  .logo {
    width: 156px;
    height: 25px;
  }

  .socials {
    display: flex; gap: 16px;

    button {
      width: 35px; height: 35px; border-radius: 9999px;
      background: var(--white-10-op);
      border: 1px solid var(--neutral-600);
      cursor: pointer;
    }
  }
}

/* ——— columns grid ——— */
.columns {
  display: grid;
  grid-template-columns: repeat(6, minMax(0, 1fr));
  column-gap: 32px;

  @include tablet {
    grid-template-columns: repeat(3, minMax(236px, 1fr));
    grid-template-rows: repeat(2, max-content);
    row-gap: 42px;
    column-gap: 16px;
  }

  @include mobile {
    display: none;
  }
}

.colTitle {
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-sm-md);
  line-height: 28px;
  margin-bottom: 12px;
  color: var(--white);
}

.colList {
  display: grid;
  gap: 12px;
}

.colItem a {
  color: var(--neutral-200);
  cursor: pointer;
  font-size: var(--font-size-sm);

  &:hover{
    color: var(--neutral-100);
  }
}

/* ——— legal ——— */
.legal {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 48px;
  color: var(--neutral-400);
  font-size: var(--font-size-xs);
  line-height: 20px;
  margin-top: 48px;

  @include tablet {
    grid-template-columns: 1fr;
    gap: 24px
  }

  @include mobile {
    grid-template-columns: 1fr;
    gap: 24px
  }
}

/* ——— bottom bar ——— */
.bottomBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  margin-top: 40px;

  @include tablet {
    margin-top: 32px;
  }

  @include mobile {
    flex-direction: column-reverse;
    align-items: flex-start;
    gap: 32px;
  }
}

.badges { display: flex;
  gap: 28px;
}
