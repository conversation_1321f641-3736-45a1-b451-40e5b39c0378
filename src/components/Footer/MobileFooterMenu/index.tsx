import { type MouseEvent } from 'react';
import { Collapse } from 'antd';
import type { CollapseProps } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import s from './index.module.scss';
import { Link } from 'react-router-dom';

export type Column = { title: string; items: string[] };

type Props = {
  columns: Column[];
  buildHref?: (section: string, item: string) => string | undefined;
  onItemClick?: (section: string, item: string, e: MouseEvent<HTMLAnchorElement>) => void;
  accordion?: boolean;
  defaultActiveKey?: string | string[];
};

export const MobileFooterMenu = ({
  columns,
  buildHref,
  onItemClick,
  accordion = true,
  defaultActiveKey,
}: Props) => {
  const items: CollapseProps['items'] = columns.map((col) => ({
    key: col.title,
    label: (
      <div className={s.headerInner}>
        <span className={s.headerText}>{col.title}</span>
      </div>
    ),
    children: (
      <ul className={s.list}>
        {col.items.map((it) => {
          const href = buildHref?.(col.title, it);
          return (
            <li key={it} className={s.listItem}>
              {href ? (
                <Link to={href} className={s.link} onClick={(e) => onItemClick?.(col.title, it, e)}>
                  {it}
                </Link>
              ) : (
                <Link
                  to="#"
                  className={s.link}
                  onClick={(e) => {
                    e.preventDefault();
                    onItemClick?.(col.title, it, e);
                  }}
                >
                  {it}
                </Link>
              )}
            </li>
          );
        })}
      </ul>
    ),
  }));

  return (
    <div className={s.wrapper}>
      <Collapse
        accordion={accordion}
        defaultActiveKey={defaultActiveKey}
        ghost
        items={items}
        expandIconPosition="end"
        expandIcon={({ isActive }) => (
          <DownOutlined className={s.chevron} rotate={isActive ? 180 : 0} />
        )}
      />
    </div>
  );
};
