import { Select } from 'antd';

import NoteIcon from '../../assets/icons/note.svg?react';

import styles from './index.module.scss';
import { oddsFormatOptions } from '../../utils/constants.ts';

export const OddsTypeSelector = () => {
  return (
    <div className={styles.container}>
      <Select
        options={oddsFormatOptions}
        defaultValue="decimal"
        prefix={
          <div className={styles.prefixContainer}>
            <NoteIcon />

            <span className={styles.prefixText}>Odds:</span>
          </div>
        }
      />
    </div>
  );
};
