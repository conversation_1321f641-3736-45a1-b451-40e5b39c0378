@use '../../../styles/media' as *;

.container {
  padding: 24px 12px;

  & .navTitle {
    font-weight: var(--font-weight-semi-bold);
    font-size: var(--font-size-sm);
    line-height: 20px;
    color: var(--white);
    opacity: 0.5;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
  }
}

.sidebarNavMenu {
  width: 100%;
  background-color: transparent;
  display: flex;
  flex-direction: column;
  row-gap: 4px;

  :global(.ant-menu-item) {
    height: 48px;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0 8px;
    margin: 0;
    transition: background 0.3s ease-in-out;
    position: relative;
    overflow: visible;

    &:hover {
      background: var(--neutral-600);
    }

    &:hover svg {
      fill: var(--neutral-50);
    }

    &:hover span {
      color: var(--neutral-50);
    }

    &:active {
      background: var(--neutral-600);
    }

    &:active span {
      color: var(--neutral-200);
      font-size: var(--font-size-sm);
    }

    &:active svg {
      transform: scale(0.9);
      fill: var(--neutral-300)
    }

    &::before {
      content: '';
      position: absolute;
      right: -20px;
      height: 100%;
      width: 3px;
      background-color: var(--primary-500);
      transform: scaleY(0);
      transform-origin: top;
      transition: transform 0.3s ease-in-out;
      border-top-left-radius: 8px;
      border-bottom-left-radius: 8px;
    }
  }

  :global(.ant-menu-item-selected::before) {
    transform: scaleY(1);
  }

  :global(.ant-menu-item-icon) {
    width: 24px;
    height: 24px;
    fill: var(--neutral-300);
    transition: 0.3s ease-in-out;
  }

  :global(.ant-menu-item-selected) {
    background: linear-gradient(90deg, var(--primary-750) 0%, var(--primary-750-transparent) 100%);
    border-top-color: var(--primary-500);

    &:hover {
      background: linear-gradient(90deg, var(--primary-750) 0%, var(--primary-750-transparent) 100%);
    }

    & > span {
      color: var(--white);
    }

    & > svg {
      fill: var(--primary-500)
    }
  }

  :global(.ant-menu-title-content) {
    margin: 0;
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-sm-md);
    line-height: 28px;
    color: var(--neutral-200);
    transition: 0.3s ease-in-out;
  }

  :global(.ant-menu-item-selected:hover),
  :global(.ant-menu-item-selected) {
    & > span {
      color: var(--white);
    }

    & > svg {
      fill: var(--primary-500);
    }
  }
}
