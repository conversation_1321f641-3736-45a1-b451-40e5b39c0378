import EarthIcon from '../../../assets/icons/flat-color-icons_globe.svg?react';

import LiveGamesIcon from '../../../assets/icons/headerNavigation/live-sports-top-navigation.svg?react';
import UpcomingIcon from '../../../assets/icons/upcoming-nav.svg?react';
import { Menu } from 'antd';

import styles from './index.module.scss';
import { SPORT_ICON_MAP } from '../../../utils/constants.ts';
import { SportType } from '../../../types/entities.ts';
import { IoChevronDownOutline } from 'react-icons/io5';
import clsx from 'clsx';

const LEAGUES_MOCK_LIST = [
  { id: 255555, name: 'Premier League 1', sportId: 1 },
  { id: 244444, name: 'Premier League 2', sportId: 1 },
  { id: 23667, name: 'Czech Republic. TT Star Series. Singles', sportId: 13 },
  { id: 28491, name: 'World. Setka Cup. Singles', sportId: 13 },
  { id: 23283, name: 'World. TTCup. Singles', sportId: 5 },
  { id: 19811, name: 'World. Friendly International Women', sportId: 9 },
];

const ALL_SPORTS = [
  { id: 1, name: SportType.FOOTBALL },
  { id: 2, name: SportType.BASKETBALL },
  { id: 5, name: SportType.ICE_HOCKEY },
  { id: 6, name: SportType.TENNIS },
  { id: 7, name: SportType.HANDBALL },
  { id: 8, name: SportType.AMERICAN_FOOTBALL },
  { id: 9, name: SportType.CRICKET },
  { id: 10, name: SportType.VOLLEYBALL },
  { id: 13, name: SportType.BASEBALL },
  { id: 24, name: SportType.TABLE_TENNIS },
  { id: 15, name: SportType.ESPORTS },
];

const DEFAULT_SUBITEMS = (sportId: number) => [
  { key: `${sportId}-liveGames`, label: 'Live matches', icon: <LiveGamesIcon /> },
  { key: `${sportId}-upcomingGames`, label: 'Upcoming', icon: <UpcomingIcon /> },
];

const DEFAULT_LAST_SUBITEM = (sportId: number) => ({
  key: `${sportId}-viewAll`,
  label: 'View all >',
});

const getSportSubItems = (sportId: number) => {
  const foundLeagues = LEAGUES_MOCK_LIST.filter((l) => l.sportId === sportId).map((l) => ({
    key: `league-${sportId}-${l.id}`,
    label: l.name,
    icon: <EarthIcon />,
  }));

  return [...DEFAULT_SUBITEMS(sportId), ...foundLeagues, DEFAULT_LAST_SUBITEM(sportId)];
};

const upcomingSportOptions = ALL_SPORTS.map((sport) => {
  const sportImage = SPORT_ICON_MAP[sport.name];

  return {
    key: sport.id,
    label: sport.name,
    children: getSportSubItems(sport.id),
    icon: <img src={sportImage} alt={sport.name} />,
  };
});

export const TopSportsNavigation = () => {
  return (
    <div className={styles.container}>
      <h3 className={styles.navTitle}>Top sports</h3>
      <Menu
        mode="inline"
        items={upcomingSportOptions}
        theme="dark"
        defaultSelectedKeys={['sports']}
        className={styles.sidebarTopSportsMenu}
        expandIcon={({ isOpen }) => (
          <div className={clsx(styles.arrowIconContainer, isOpen && styles.open)}>
            <IoChevronDownOutline
              className={clsx(styles.arrowIcon, isOpen ? styles.arrowUp : styles.arrowDown)}
            />
          </div>
        )}
      />
    </div>
  );
};
