import { Menu } from 'antd';
import { Link } from 'react-router-dom';
import { FOOTER_MENU_ITEMS } from '../../utils/constants';
import styles from './index.module.scss';

export const MobileBottomTabs = () => {
  const footerMenuItems = FOOTER_MENU_ITEMS.map((item) => ({
    key: item.key,
    icon: <item.icon />,
    label: <Link to={item.path}>{item.label}</Link>,
  }));

  return (
    <Menu
      mode="horizontal"
      items={footerMenuItems}
      theme="dark"
      defaultSelectedKeys={['sports']}
      className={styles.footerNavMenu}
    />
  );
};
