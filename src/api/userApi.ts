import type { User } from './authApi';
import { api } from './index.ts';

interface UpdateProfileRequest {
  name: string;
  surname: string;
  username: string;
  birthday: string;
}

interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface ChangePasswordResponse {
  message: string;
}

interface NotificationSettings {
  emailMarketing: boolean;
  bonusNotifications: boolean;
  winNotifications: boolean;
  depositNotifications: boolean;
  withdrawalNotifications: boolean;
}

type UpdateNotificationSettingsRequest = NotificationSettings;

interface UpdateNotificationSettingsResponse {
  message: string;
  settings: NotificationSettings;
}

// User API slice - manages user profile, settings, and account operations
export const userApi = api.injectEndpoints({
  endpoints: (builder) => ({
    updateProfile: builder.mutation<User, UpdateProfileRequest>({
      query: (data) => ({
        url: '/auth/profile',
        method: 'PUT',
        body: data,
      }),
    }),

    changePassword: builder.mutation<ChangePasswordResponse, ChangePasswordRequest>({
      query: (data) => ({
        url: '/auth/change-password',
        method: 'POST',
        body: data,
      }),
    }),

    getNotificationSettings: builder.query<NotificationSettings, void>({
      query: () => ({
        url: '/user/notification-settings',
        method: 'GET',
      }),
      // TODO: add NotificationSettings tag
      providesTags: ['NotificationSettings'],
    }),

    updateNotificationSettings: builder.mutation<
      UpdateNotificationSettingsResponse,
      UpdateNotificationSettingsRequest
    >({
      query: (data) => ({
        url: '/user/notification-settings',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['NotificationSettings'],
    }),
  }),
});

export const {
  useUpdateProfileMutation,
  useChangePasswordMutation,
  useGetNotificationSettingsQuery,
  useUpdateNotificationSettingsMutation,
} = userApi;

export type { NotificationSettings };
