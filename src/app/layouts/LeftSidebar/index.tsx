import styles from './index.module.scss';

import 'simplebar-react/dist/simplebar.min.css';
import SimpleBar from 'simplebar-react';

import AppLogo from '../../../assets/images/Logo.png';
import { SideBarNavigation } from '../../../components/navigation/SidebarNavigation';
import { TopSportsNavigation } from '../../../components/navigation/TopSportsNavigation';
import { AccountNavigation } from '../../../components/navigation/AccountNavigation';
import { SidebarExtraOptions } from '../SidebarExtraOptions';

export const LeftSidebar = () => {
  return (
    <SimpleBar className={styles.sidebar}>
      <div className="sidebar-content">
        <aside className={styles.leftSidebar}>
          <img src={AppLogo} alt="App Logo" className={styles.appLogo} />

          <SideBarNavigation />
          <div className={styles.sectionSeparator} />
          <TopSportsNavigation />
          <div className={styles.sectionSeparator} />
          <AccountNavigation />
          <div className={styles.sectionSeparator} />
          <SidebarExtraOptions />
        </aside>
      </div>
    </SimpleBar>
  );
};
