import { type FC } from 'react';
import { Outlet } from 'react-router-dom';

import styles from './index.module.scss';
import { LeftSidebar } from '../LeftSidebar';
import { RightSidebar } from '../RightSidebar';
import { Header } from '../Header';
import { MobileBottomTabs } from '../MobileBottomTabs';
import { Footer } from '../Footer';

export const MainLayout: FC = () => {
  return (
    <div className={styles.container}>
      <LeftSidebar />
      <div className={styles.mainWrapper}>
        <Header />
        <div className={styles.contentWrapper}>
          <div className={styles.mainContent}>
            <Outlet />
          </div>

          <RightSidebar />
        </div>
        <Footer />
      </div>

      <MobileBottomTabs />
    </div>
  );
};
