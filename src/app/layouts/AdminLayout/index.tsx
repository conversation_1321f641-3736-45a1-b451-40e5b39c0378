import type { <PERSON> } from 'react';
import { Outlet } from 'react-router-dom';
import { Header } from '../../../components/Header';
import { LeftSidebar } from '../../../components/LeftSidebar';
import { Footer } from '../../../components/Footer';

import styles from './index.module.scss';

export const AdminLayout: FC = () => {
  return (
    <div className={styles.container}>
      <LeftSidebar />
      <div className={styles.mainWrapper}>
        <Header />
        <div className={styles.contentWrapper}>
          <main className={styles.mainContent}>
            <Outlet />
          </main>
        </div>
        <Footer />
      </div>
      <div className={styles.yellowCircle} />
    </div>
  );
};
